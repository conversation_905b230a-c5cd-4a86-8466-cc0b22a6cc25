"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Clock, Search, ArrowRight, HelpCircle } from "lucide-react";
import { BlogPost } from "@/lib/blog";


interface BlogListProps {
  dict: any;
  lang: 'en' | 'fr';
  posts: BlogPost[];
}

export function BlogList({ dict, lang, posts }: BlogListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredPosts, setFilteredPosts] = useState(posts);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (!term) {
      setFilteredPosts(posts);
      return;
    }
    
    const filtered = posts.filter((post: BlogPost) =>
      post.title.toLowerCase().includes(term.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(term.toLowerCase()) ||
      post.tags.some((tag: string) => tag.toLowerCase().includes(term.toLowerCase()))
    );
    setFilteredPosts(filtered);
  };

  const featuredPosts = filteredPosts.filter((post: BlogPost) => post.featured);
  const regularPosts = filteredPosts.filter((post: BlogPost) => !post.featured);

  return (
    <div className="flex min-h-screen w-full flex-col bg-background pt-16">
      
      <div className="w-full max-w-6xl mx-auto px-4 sm:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center items-center gap-6 mb-4">
            <Link href={`/${lang}`} className="inline-flex items-center text-primary hover:text-primary/80">
              <ArrowRight className="h-4 w-4 mr-2 rotate-180" />
              {dict.navigation.home}
            </Link>
            <Link href={`/${lang}/faq`} className="inline-flex items-center text-muted-foreground hover:text-primary">
              <HelpCircle className="h-4 w-4 mr-2" />
              {dict.navigation.faq}
            </Link>
          </div>
          <h1 className="text-4xl sm:text-5xl font-bold text-primary tracking-tight mb-4">
            {dict.blog.title}
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            {dict.blog.description}
          </p>
        </div>

        {/* Search */}
        <div className="max-w-md mx-auto mb-12">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder={dict.blog.searchPlaceholder}
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Featured Posts */}
        {featuredPosts.length > 0 && (
          <section className="mb-16">
            <h2 className="text-2xl font-bold mb-8">{dict.blog.featuredPosts}</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {featuredPosts.map((post: BlogPost) => (
                <BlogCard key={post.slug} post={post} dict={dict} lang={lang} featured />
              ))}
            </div>
          </section>
        )}

        {/* Regular Posts */}
        {regularPosts.length > 0 && (
          <section>
            <h2 className="text-2xl font-bold mb-8">
              {featuredPosts.length > 0 ? (dict.blog.moreArticles || "More Articles") : (dict.blog.allArticles || "All Articles")}
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {regularPosts.map((post: BlogPost) => (
                <BlogCard key={post.slug} post={post} dict={dict} lang={lang} />
              ))}
            </div>
          </section>
        )}

        {/* No Results */}
        {filteredPosts.length === 0 && (
          <div className="text-center py-16">
            <Search className="mx-auto h-16 w-16 text-muted-foreground/50 mb-4" />
            <h3 className="text-xl font-semibold mb-2">{dict.blog.noResults}</h3>
            <p className="text-muted-foreground">
              {dict.blog.noResultsDescription || "Try adjusting your search terms or browse all articles."}
            </p>
            <Button
              variant="outline"
              onClick={() => handleSearch("")}
              className="mt-4"
            >
              {dict.blog.clearSearch || "Clear Search"}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

interface BlogCardProps {
  post: BlogPost;
  dict: any;
  lang: 'en' | 'fr';
  featured?: boolean;
}

function BlogCard({ post, dict, lang, featured = false }: BlogCardProps) {
  return (
    <Card className={`h-full hover:shadow-lg transition-shadow ${featured ? 'border-primary/20' : ''}`}>
      <CardHeader>
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
          <Clock className="h-4 w-4" />
          <span>{post.readTime} {dict.blog.readTime}</span>
          <span>•</span>
          <span>{new Date(post.publishedAt).toLocaleDateString(lang === 'fr' ? 'fr-FR' : 'en-US')}</span>
        </div>
        <CardTitle className={featured ? 'text-xl' : 'text-lg'}>
          <Link 
            href={`/${lang}/blog/${post.slug}`}
            className="hover:text-primary transition-colors"
          >
            {post.title}
          </Link>
        </CardTitle>
        <CardDescription className="line-clamp-3">
          {post.excerpt}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2 mb-4">
          {post.tags.slice(0, 3).map((tag: string) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">
            {dict.blog.author} {post.author}
          </span>
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/${lang}/blog/${post.slug}`}>
              {dict.blog.readMore}
              <ArrowRight className="h-4 w-4 ml-1" />
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
