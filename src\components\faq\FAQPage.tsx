"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  ArrowRight, 
  BookOpen, 
  Calculator, 
  Building, 
  CreditCard, 
  TrendingUp,
  HelpCircle,
  Star
} from "lucide-react";
import { FAQItem, FAQCategory, getFAQItems, getFAQCategories, searchFAQs, getFeaturedFAQs } from "@/lib/faq";
import LanguageSwitcher from "../LanguageSwitcher";

interface FAQPageProps {
  dict: any;
  lang: 'en' | 'fr';
  faqs: FAQItem[];
  categories: FAQCategory[];
  featuredFAQs: FAQItem[];
}

const iconMap = {
  BookOpen,
  Calculator,
  Building,
  CreditCard,
  TrendingUp,
  HelpCircle
};

export function FAQPage({ dict, lang, faqs, categories, featuredFAQs }: FAQPageProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredFAQs, setFilteredFAQs] = useState(faqs);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [isSearching, setIsSearching] = useState(false);

  // Generate FAQ structured data for SEO
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer.replace(/\*\*(.*?)\*\*/g, '$1').replace(/\*(.*?)\*/g, '$1')
      }
    }))
  };

  const handleSearch = async (term: string) => {
    setSearchTerm(term);
    setIsSearching(true);
    
    if (!term) {
      setFilteredFAQs(selectedCategory === "all" ? faqs : faqs.filter(faq => faq.category === selectedCategory));
      setIsSearching(false);
      return;
    }
    
    try {
      const searchResults = await searchFAQs(term, lang);
      setFilteredFAQs(searchResults);
    } catch (error) {
      console.error('Search error:', error);
      setFilteredFAQs([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    if (searchTerm) {
      handleSearch(searchTerm);
    } else {
      setFilteredFAQs(category === "all" ? faqs : faqs.filter(faq => faq.category === category));
    }
  };

  const formatAnswer = (answer: string) => {
    return answer
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      .replace(/`(.*?)`/g, '<code class="bg-muted px-1 py-0.5 rounded text-sm">$1</code>')
      .split('\n\n')
      .map(paragraph => {
        if (paragraph.includes('**') || paragraph.includes('|') || paragraph.includes('-')) {
          return paragraph;
        }
        return `<p class="mb-4 leading-relaxed">${paragraph}</p>`;
      })
      .join('\n');
  };

  return (
    <>
      {/* FAQ Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
      />

      <div className="flex min-h-screen w-full flex-col bg-background pt-16">
      
      <div className="w-full max-w-6xl mx-auto px-4 sm:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <Link href={`/${lang}`} className="inline-flex items-center text-primary hover:text-primary/80 mb-4">
            <ArrowRight className="h-4 w-4 mr-2 rotate-180" />
            {dict.navigation.home}
          </Link>
          <h1 className="text-4xl sm:text-5xl font-bold text-primary tracking-tight mb-4">
            {dict.faq.title}
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            {dict.faq.description}
          </p>
        </div>

        {/* Search */}
        <div className="max-w-md mx-auto mb-12">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder={dict.faq.searchPlaceholder}
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Featured FAQs */}
        {!searchTerm && featuredFAQs.length > 0 && (
          <section className="mb-16">
            <div className="flex items-center gap-2 mb-8">
              <Star className="h-5 w-5 text-yellow-500" />
              <h2 className="text-2xl font-bold">{dict.faq.featuredQuestions}</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              {featuredFAQs.map((faq) => (
                <Card key={faq.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="text-lg">{faq.question}</CardTitle>
                    <div className="flex flex-wrap gap-2">
                      {faq.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div 
                      className="text-muted-foreground line-clamp-3 mb-4"
                      dangerouslySetInnerHTML={{ 
                        __html: formatAnswer(faq.answer.split('\n\n')[0]) 
                      }}
                    />
                    <Button variant="ghost" size="sm" onClick={() => {
                      const element = document.getElementById(`faq-${faq.id}`);
                      element?.scrollIntoView({ behavior: 'smooth' });
                    }}>
                      {dict.faq.readFullAnswer}
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}

        {/* Categories and FAQs */}
        <Tabs value={selectedCategory} onValueChange={handleCategoryChange} className="w-full">
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6 mb-8">
            <TabsTrigger value="all" className="flex items-center gap-2">
              <HelpCircle className="h-4 w-4" />
              <span className="hidden sm:inline">{dict.faq.allCategories}</span>
              <span className="sm:hidden">All</span>
            </TabsTrigger>
            {categories.map((category) => {
              const IconComponent = iconMap[category.icon as keyof typeof iconMap] || HelpCircle;
              return (
                <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-2">
                  <IconComponent className="h-4 w-4" />
                  <span className="hidden lg:inline">{category.name}</span>
                  <span className="lg:hidden">{category.name.split(' ')[0]}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          <TabsContent value="all" className="mt-0">
            <FAQList faqs={filteredFAQs} dict={dict} formatAnswer={formatAnswer} isSearching={isSearching} />
          </TabsContent>
          
          {categories.map((category) => (
            <TabsContent key={category.id} value={category.id} className="mt-0">
              <div className="mb-6">
                <h3 className="text-xl font-semibold mb-2">{category.name}</h3>
                <p className="text-muted-foreground">{category.description}</p>
              </div>
              <FAQList 
                faqs={filteredFAQs.filter(faq => faq.category === category.id)} 
                dict={dict} 
                formatAnswer={formatAnswer}
                isSearching={isSearching}
              />
            </TabsContent>
          ))}
        </Tabs>

        {/* Call to Action */}
        <Card className="mt-16 bg-primary/5 border-primary/20">
          <CardContent className="p-8 text-center">
            <Calculator className="mx-auto h-12 w-12 text-primary mb-4" />
            <h3 className="text-xl font-semibold mb-2">
              {dict.faq.calculatorCTA}
            </h3>
            <p className="text-muted-foreground mb-4">
              {dict.faq.calculatorDescription}
            </p>
            <Button asChild size="lg">
              <Link href={`/${lang}`}>
                {dict.faq.tryCalculator}
                <Calculator className="h-4 w-4 ml-2" />
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
    </>
  );
}

interface FAQListProps {
  faqs: FAQItem[];
  dict: any;
  formatAnswer: (answer: string) => string;
  isSearching: boolean;
}

function FAQList({ faqs, dict, formatAnswer, isSearching }: FAQListProps) {
  if (isSearching) {
    return (
      <div className="text-center py-8">
        <Search className="mx-auto h-8 w-8 text-muted-foreground/50 mb-2 animate-pulse" />
        <p className="text-muted-foreground">{dict.faq.searching}</p>
      </div>
    );
  }

  if (faqs.length === 0) {
    return (
      <div className="text-center py-16">
        <HelpCircle className="mx-auto h-16 w-16 text-muted-foreground/50 mb-4" />
        <h3 className="text-xl font-semibold mb-2">{dict.faq.noResults}</h3>
        <p className="text-muted-foreground">
          {dict.faq.noResultsDescription}
        </p>
      </div>
    );
  }

  return (
    <Accordion type="single" collapsible className="w-full space-y-4">
      {faqs.map((faq) => (
        <AccordionItem 
          key={faq.id} 
          value={faq.id} 
          id={`faq-${faq.id}`}
          className="border rounded-lg px-6"
        >
          <AccordionTrigger className="text-left hover:no-underline">
            <div className="flex items-start gap-4 w-full">
              <div className="flex-1">
                <h3 className="font-semibold text-lg mb-2">{faq.question}</h3>
                <div className="flex flex-wrap gap-2">
                  {faq.tags.slice(0, 4).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-4">
            <div 
              className="prose prose-sm max-w-none text-muted-foreground"
              dangerouslySetInnerHTML={{ __html: formatAnswer(faq.answer) }}
            />
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}
